import Hero from '../models/hero.model.js';
import sharp from 'sharp';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) fs.mkdirSync(uploadsDir, { recursive: true });

// A more sophisticated image processing pipeline.
const processImage = async (buffer) => {
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1E9)}`;
    const filename = `hero-${uniqueSuffix}.webp`;
    const filepath = path.join(uploadsDir, filename);

    const thumbFilename = `thumb-${uniqueSuffix}.webp`;
    const thumbFilepath = path.join(uploadsDir, thumbFilename);
    
    // New: A tiny placeholder for the blur-up effect.
    const blurFilename = `blur-${uniqueSuffix}.webp`;
    const blurFilepath = path.join(uploadsDir, blurFilename);

    // --- The Magic is Here ---
    // Main Image: Resized, sharpened for crispness, and optimized.
    // We add .sharpen() to counteract the softening from resizing.
    // The `effort` option tells WebP to work harder to find the best compression.
    await sharp(buffer)
        .resize(1920, 1080, { fit: 'inside', withoutEnlargement: true })
        .sharpen({ sigma: 0.5, m1: 1, m2: 2, x1: 2, y2: 10, y3: 20 }) // A gentle but effective sharpen
        .toFormat('webp', { quality: 85, effort: 6 }) // Increased quality slightly, with max effort
        .toFile(filepath);

    // Thumbnail: Unchanged, still perfect for the filmstrip.
    await sharp(buffer)
        .resize(400, 225, { fit: 'cover' })
        .toFormat('webp', { quality: 60 })
        .toFile(thumbFilepath);
        
    // New Blur Placeholder: Very small, very low quality, for instant loading.
    await sharp(buffer)
        .resize(32, 18, { fit: 'inside' })
        .toFormat('webp', { quality: 20 })
        .toFile(blurFilepath);

    return {
        imageUrl: `/uploads/${filename}`,
        thumbnailUrl: `/uploads/${thumbFilename}`,
        blurUrl: `/uploads/${blurFilename}`, // We now return a third URL.
    };
};

export const getSlides = async (req, res) => {
    try {
        const slides = await Hero.find().sort({ createdAt: -1 });
        res.status(200).json(slides);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

export const createSlide = async (req, res) => {
    const { title, category, description } = req.body;
    if (!req.file) return res.status(400).json({ message: 'Image is required' });
    try {
        // We now get blurUrl from our processing function.
        const { imageUrl, thumbnailUrl, blurUrl } = await processImage(req.file.buffer);
        const newSlide = new Hero({ title, category, description, imageUrl, thumbnailUrl, blurUrl });
        await newSlide.save();
        res.status(201).json(newSlide);
    } catch (error) { res.status(500).json({ message: error.message }); }
};

export const updateSlide = async (req, res) => {
    const { id } = req.params;
    const { title, category, description } = req.body;
    let imagePaths = {};

    try {
        const slideToUpdate = await Hero.findById(id);
        if (!slideToUpdate) return res.status(404).json({ message: 'Slide not found' });
        
        if (req.file) {
            imagePaths = await processImage(req.file.buffer);
            // Clean up all old images, including the blur placeholder.
            if (fs.existsSync(path.join(__dirname, '..', slideToUpdate.imageUrl))) fs.unlinkSync(path.join(__dirname, '..', slideToUpdate.imageUrl));
            if (fs.existsSync(path.join(__dirname, '..', slideToUpdate.thumbnailUrl))) fs.unlinkSync(path.join(__dirname, '..', slideToUpdate.thumbnailUrl));
            if (slideToUpdate.blurUrl && fs.existsSync(path.join(__dirname, '..', slideToUpdate.blurUrl))) fs.unlinkSync(path.join(__dirname, '..', slideToUpdate.blurUrl));
        }

        // We add blurUrl to the updated data if it exists.
        const updatedData = { title, category, description, ...imagePaths };
        const updatedSlide = await Hero.findByIdAndUpdate(id, updatedData, { new: true });
        res.status(200).json(updatedSlide);
    } catch (error) { res.status(500).json({ message: error.message }); }
};

export const deleteSlide = async (req, res) => {
    try {
        const slide = await Hero.findByIdAndDelete(req.params.id);
        if (!slide) return res.status(404).json({ message: 'Slide not found' });
        // Ensure all three images are deleted.
        if (fs.existsSync(path.join(__dirname, '..', slide.imageUrl))) fs.unlinkSync(path.join(__dirname, '..', slide.imageUrl));
        if (fs.existsSync(path.join(__dirname, '..', slide.thumbnailUrl))) fs.unlinkSync(path.join(__dirname, '..', slide.thumbnailUrl));
        if (slide.blurUrl && fs.existsSync(path.join(__dirname, '..', slide.blurUrl))) fs.unlinkSync(path.join(__dirname, '..', slide.blurUrl));
        res.status(200).json({ message: 'Slide deleted successfully' });
    } catch (error) { res.status(500).json({ message: error.message }); }
};