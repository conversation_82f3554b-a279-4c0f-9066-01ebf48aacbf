// frontend/src/pages/Dashboard/Info/components/about/AboutPanel.jsx
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  User,
  Building,
  Target,
  Eye,
  Heart,
  Users,
  Calendar,
  Plus,
  Trash2,
  Edit3,
  Save,
  X
} from 'lucide-react';
import './AboutPanel.css';

const AboutPanel = ({ aboutData, onAboutChange }) => {
  const [activeSection, setActiveSection] = useState('hero');
  const [editingTeamMember, setEditingTeamMember] = useState(null);
  const [editingStoryMilestone, setEditingStoryMilestone] = useState(null);

  // Handle input changes for nested objects
  const handleNestedChange = (section, field, value) => {
    onAboutChange({
      target: {
        name: `aboutUs.${section}.${field}`,
        value: value
      }
    });
  };

  // Handle array changes (values, team members, story milestones)
  const handleArrayChange = (arrayName, index, field, value) => {
    const currentArray = aboutData[arrayName] || [];
    const updatedArray = [...currentArray];

    if (field) {
      updatedArray[index] = { ...updatedArray[index], [field]: value };
    } else {
      updatedArray[index] = value;
    }

    onAboutChange({
      target: {
        name: `aboutUs.${arrayName}`,
        value: updatedArray
      }
    });
  };

  // Add new item to array
  const addArrayItem = (arrayName, defaultItem) => {
    const currentArray = aboutData[arrayName] || [];
    onAboutChange({
      target: {
        name: `aboutUs.${arrayName}`,
        value: [...currentArray, defaultItem]
      }
    });
  };

  // Remove item from array
  const removeArrayItem = (arrayName, index) => {
    const currentArray = aboutData[arrayName] || [];
    const updatedArray = currentArray.filter((_, i) => i !== index);
    onAboutChange({
      target: {
        name: `aboutUs.${arrayName}`,
        value: updatedArray
      }
    });
  };

  // Handle nested object changes (like socials in team members)
  const handleNestedObjectChange = (arrayName, index, objectField, field, value) => {
    const currentArray = aboutData[arrayName] || [];
    const updatedArray = [...currentArray];
    updatedArray[index] = {
      ...updatedArray[index],
      [objectField]: {
        ...updatedArray[index][objectField],
        [field]: value
      }
    };

    onAboutChange({
      target: {
        name: `aboutUs.${arrayName}`,
        value: updatedArray
      }
    });
  };

  const sections = [
    { id: 'hero', name: 'Hero Section', icon: <Building className="w-4 h-4" /> },
    { id: 'company', name: 'Company Info', icon: <User className="w-4 h-4" /> },
    { id: 'mission', name: 'Mission & Vision', icon: <Target className="w-4 h-4" /> },
    { id: 'values', name: 'Values', icon: <Heart className="w-4 h-4" /> },
    { id: 'story', name: 'Story Timeline', icon: <Calendar className="w-4 h-4" /> },
    { id: 'team', name: 'Team Members', icon: <Users className="w-4 h-4" /> }
  ];

  const renderHeroSection = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-slate-800 flex items-center gap-2">
        <Building className="w-5 h-5 text-blue-500" />
        Hero Section
      </h3>

      <div className="grid grid-cols-1 gap-4">
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">Hero Title</label>
          <input
            type="text"
            value={aboutData.heroTitle || ''}
            onChange={(e) => onAboutChange({ target: { name: 'aboutUs.heroTitle', value: e.target.value } })}
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Know About Us"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">Hero Description</label>
          <textarea
            value={aboutData.heroDescription || ''}
            onChange={(e) => onAboutChange({ target: { name: 'aboutUs.heroDescription', value: e.target.value } })}
            rows={4}
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="We are a team of passionate creators..."
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Button Text</label>
            <input
              type="text"
              value={aboutData.heroButtonText || ''}
              onChange={(e) => onAboutChange({ target: { name: 'aboutUs.heroButtonText', value: e.target.value } })}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Contact Us"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Button Link</label>
            <input
              type="text"
              value={aboutData.heroButtonLink || ''}
              onChange={(e) => onAboutChange({ target: { name: 'aboutUs.heroButtonLink', value: e.target.value } })}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="/contact"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderCompanySection = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-slate-800 flex items-center gap-2">
        <User className="w-5 h-5 text-blue-500" />
        Company Information
      </h3>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">Company Name</label>
          <input
            type="text"
            value={aboutData.companyName || ''}
            onChange={(e) => onAboutChange({ target: { name: 'aboutUs.companyName', value: e.target.value } })}
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Your Company"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">Founded Year</label>
          <input
            type="text"
            value={aboutData.foundedYear || ''}
            onChange={(e) => onAboutChange({ target: { name: 'aboutUs.foundedYear', value: e.target.value } })}
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="2020"
          />
        </div>
      </div>
    </div>
  );

  const renderMissionVisionSection = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-slate-800 flex items-center gap-2">
        <Target className="w-5 h-5 text-blue-500" />
        Mission & Vision
      </h3>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">Mission Statement</label>
          <textarea
            value={aboutData.mission || ''}
            onChange={(e) => onAboutChange({ target: { name: 'aboutUs.mission', value: e.target.value } })}
            rows={3}
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="To create exceptional digital experiences..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">Vision Statement</label>
          <textarea
            value={aboutData.vision || ''}
            onChange={(e) => onAboutChange({ target: { name: 'aboutUs.vision', value: e.target.value } })}
            rows={3}
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="To be the leading force in digital innovation..."
          />
        </div>
      </div>
    </div>
  );

  const renderValuesSection = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-slate-800 flex items-center gap-2">
        <Heart className="w-5 h-5 text-blue-500" />
        Company Values
      </h3>

      <div className="space-y-3">
        {(aboutData.values || []).map((value, index) => (
          <div key={index} className="flex items-center gap-2">
            <input
              type="text"
              value={value}
              onChange={(e) => handleArrayChange('values', index, null, e.target.value)}
              className="flex-1 px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter a company value..."
            />
            <button
              type="button"
              onClick={() => removeArrayItem('values', index)}
              className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        ))}

        <button
          type="button"
          onClick={() => addArrayItem('values', '')}
          className="flex items-center gap-2 px-4 py-2 text-blue-600 border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Add Value
        </button>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'hero':
        return renderHeroSection();
      case 'company':
        return renderCompanySection();
      case 'mission':
        return renderMissionVisionSection();
      case 'values':
        return renderValuesSection();
      case 'story':
        return <div className="text-center py-8 text-slate-500">Story Timeline section coming soon...</div>;
      case 'team':
        return <div className="text-center py-8 text-slate-500">Team Members section coming soon...</div>;
      default:
        return renderHeroSection();
    }
  };

  return (
    <motion.div
      className="bg-white rounded-2xl shadow-lg shadow-slate-200/50 border border-slate-100 overflow-hidden"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      {/* Section Navigation */}
      <div className="border-b border-slate-200 p-6">
        <h2 className="text-2xl font-bold text-slate-800 mb-4">About Us Content Management</h2>
        <div className="flex flex-wrap gap-2">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeSection === section.id
                  ? 'bg-blue-100 text-blue-700 border border-blue-200'
                  : 'bg-slate-50 text-slate-600 hover:bg-slate-100 border border-slate-200'
              }`}
            >
              {section.icon}
              {section.name}
            </button>
          ))}
        </div>
      </div>

      {/* Content Area */}
      <div className="p-6">
        {renderContent()}
      </div>
    </motion.div>
  );
};

export default AboutPanel;