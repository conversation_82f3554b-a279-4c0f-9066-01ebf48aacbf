{"hash": "0a0f7a9e", "configHash": "2e64e1e0", "lockfileHash": "a289ab1e", "browserHash": "af574654", "optimized": {"axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "7a8a32d2", "needsInterop": false}, "browser-image-compression": {"src": "../../browser-image-compression/dist/browser-image-compression.mjs", "file": "browser-image-compression.js", "fileHash": "d88d5d32", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "5509d2dd", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "dd6c7da8", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "5e811dab", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b2733dad", "needsInterop": true}, "react-icons/fa": {"src": "../../../../../../../../node_modules/react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "f3e97a5a", "needsInterop": false}, "react-icons/fi": {"src": "../../../../../../../../node_modules/react-icons/fi/index.mjs", "file": "react-icons_fi.js", "fileHash": "06a96163", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "54932155", "needsInterop": false}, "swiper/modules": {"src": "../../swiper/modules/index.mjs", "file": "swiper_modules.js", "fileHash": "d1d05bbd", "needsInterop": false}, "swiper/react": {"src": "../../swiper/swiper-react.mjs", "file": "swiper_react.js", "fileHash": "b193ec88", "needsInterop": false}}, "chunks": {"chunk-MD4E26TR": {"file": "chunk-MD4E26TR.js"}, "chunk-H5FQS3OF": {"file": "chunk-H5FQS3OF.js"}, "chunk-XSDDGX2Z": {"file": "chunk-XSDDGX2Z.js"}, "chunk-KY4OSI3V": {"file": "chunk-KY4OSI3V.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}