import React, { useState, useEffect } from 'react';
import { FiArrowRight } from 'react-icons/fi'; // A clean icon for the button
import axios from 'axios';

const Hero = () => {
  const [heroData, setHeroData] = useState({
    heroTitle: 'Know About Us',
    heroDescription: 'We are a team of passionate creators, designers, and innovators dedicated to crafting exceptional digital experiences that drive results and inspire our community.',
    heroButtonText: 'Contact Us',
    heroButtonLink: '/contact'
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAboutData = async () => {
      try {
        const response = await axios.get('http://localhost:5000/api/info');
        if (response.data.aboutUs) {
          setHeroData({
            heroTitle: response.data.aboutUs.heroTitle || 'Know About Us',
            heroDescription: response.data.aboutUs.heroDescription || 'We are a team of passionate creators, designers, and innovators dedicated to crafting exceptional digital experiences that drive results and inspire our community.',
            heroButtonText: response.data.aboutUs.heroButtonText || 'Contact Us',
            heroButtonLink: response.data.aboutUs.heroButtonLink || '/contact'
          });
        }
      } catch (error) {
        console.error("Failed to fetch about data:", error);
        // Keep default values if API fails
      } finally {
        setLoading(false);
      }
    };
    fetchAboutData();
  }, []);

  if (loading) {
    return (
      <section className="relative bg-white font-sans overflow-hidden">
        <div className="min-h-[550px] flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </section>
    );
  }
  return (
      <section className="relative bg-white font-sans overflow-hidden">   
      <div 
        className="absolute top-1/4 -left-16 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl opacity-50" 
        aria-hidden="true"
      ></div>
      <div 
        className="absolute -top-16 -right-16 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-3xl opacity-50" 
        aria-hidden="true"
      ></div>
      
      {/* Content Wrapper */}
      <div className="relative z-10">
        <div className="min-h-[550px] flex flex-col justify-center items-center">
          <div className="max-w-4xl mx-auto px-4 py-24 text-center">

            {/* Main Heading */}
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold text-gray-900 tracking-tight mb-4">
              {heroData.heroTitle}
            </h1>

            {/* Description Text */}
            <p className="max-w-2xl mx-auto text-base sm:text-lg text-gray-600 leading-relaxed mb-8">
              {heroData.heroDescription}
            </p>

            {/* Call to Action Button */}
            <a
              href={heroData.heroButtonLink}
              className="
                group
                inline-flex items-center justify-center
                px-6 py-3
                font-medium text-white
                bg-blue-600 rounded-lg shadow-md
                transition-all duration-200 ease-in-out
                hover:bg-blue-700 hover:shadow-lg hover:-translate-y-0.5
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
              "
            >
              {heroData.heroButtonText}
              <FiArrowRight
                className="w-5 h-5 ml-2 transition-transform duration-200 ease-in-out group-hover:translate-x-1"
              />
            </a>
            
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;