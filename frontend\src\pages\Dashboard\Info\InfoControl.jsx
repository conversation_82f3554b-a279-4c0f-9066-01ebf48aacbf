// frontend/src/admin/InfoControl.jsx
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { motion } from 'framer-motion';
import { Save, Loader, AlertCircle, Sparkles, ServerCrash } from 'lucide-react';
import './InfoControl.css';

// Import all our specialized panels
import HeroPanel from './components/contact/hero/HeroPanel';
// CORRECTED: Updated the import path for the moved component
import ContactDetailsPanel from './components/contact/coreContact/ContactDetailsPanel';
import FormLabelsPanel from './components/contact/form/FormLabelsPanel';
import FaqPanel from './components/contact/FaqPanel';
import AboutPanel from './components/about/AboutPanel';

const InfoControl = () => {
  // ... (All existing state and functions remain the same)
  const [activeTab, setActiveTab] = useState('contact');
  const [formData, setFormData] = useState({
    heroTitle: '', heroSubtitle: '',
    email: '', phone: '', address: '', googleMapsUrl: '',
    formLabels: { name: '', email: '', message: '' },
    faqs: []
  });
  const [newFaqIndex, setNewFaqIndex] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');

  const API_URL = 'http://localhost:5000/api/info';

  useEffect(() => {
    const fetchInfo = async () => {
      try {
        setError(null); setIsLoading(true);
        const response = await axios.get(API_URL);
        setFormData({
            ...response.data,
            faqs: response.data.faqs || [],
            formLabels: response.data.formLabels || { name: '', email: '', message: '' }
        });
      } catch (err) { setError('Failed to fetch page information. Please check your connection.'); } 
      finally { setIsLoading(false); }
    };
    fetchInfo();
  }, []);

  const handleInputChange = (e) => setFormData(prev => ({ ...prev, [e.target.name]: e.target.value }));
  
  const handleFormLabelChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
        ...prev,
        formLabels: { ...prev.formLabels, [name]: value }
    }));
  };
  
  const handleFaqChange = (index, field, value) => {
    const updatedFaqs = formData.faqs.map((faq, i) => i === index ? { ...faq, [field]: value } : faq);
    setFormData(prev => ({ ...prev, faqs: updatedFaqs }));
  };
  
  const addFaq = () => {
    const newFaqs = [...formData.faqs, { question: '', answer: '' }];
    setFormData(prev => ({ ...prev, faqs: newFaqs }));
    setNewFaqIndex(newFaqs.length - 1);
  };
  
  const removeFaq = (index) => {
    setFormData(prev => ({ ...prev, faqs: prev.faqs.filter((_, i) => i !== index) }));
    setNewFaqIndex(null);
  };

  const handleConfirmNewFaq = () => setNewFaqIndex(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (activeTab === 'contact' && newFaqIndex !== null) {
      setError("Please confirm your new FAQ before saving.");
      return;
    }

    try {
      setError(null); setSuccessMessage(''); setIsSaving(true);
      await axios.put(API_URL, formData);
      setSuccessMessage(`${activeTab === 'contact' ? 'Contact' : 'About Us'} information updated successfully!`);
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err) { setError('Save failed. Please ensure all fields are filled correctly.'); }
    finally { setIsSaving(false); }
  };
  // ... (The rest of the component logic remains identical)

  if (isLoading) { /* ... */ }
  if (error && !formData.email) { /* ... */ }

  const isSaveDisabled = isSaving || (activeTab === 'contact' && newFaqIndex !== null);

  return (
    <div className="min-h-screen bg-slate-50/50 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* ... (Header and Tabs are unchanged) ... */}
        <div className="mb-10">
          <h1 className="text-4xl md:text-5xl font-bold text-slate-800 tracking-tighter">Site Information</h1>
          <p className="text-slate-500 mt-2 text-lg">Manage public-facing content for key pages.</p>
        </div>
        
        <div className="relative mb-8">
          <div className="flex space-x-2 border-b border-slate-200">
            {['contact', 'about'].map((tab) => (
              <button key={tab} type="button" onClick={() => setActiveTab(tab)} className={`relative py-3 px-4 font-semibold text-slate-600 transition-colors duration-300 z-10 ${activeTab === tab ? 'text-indigo-600' : 'hover:text-slate-800'}`}>
                <span className="capitalize">{tab} Page</span>
                {activeTab === tab && <motion.div className="absolute inset-0 bg-white rounded-t-lg shadow-sm" layoutId="active-pill" transition={{ type: 'spring', stiffness: 380, damping: 30 }} />}
              </button>
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="pb-32">
          {activeTab === 'contact' && (
            <div className="flex flex-col gap-8">
              {/* The Hero Panel is still at the top */}
              <HeroPanel heroData={formData} onFormChange={handleInputChange} />
              
              {/* FIXED: Passed the handleInputChange function to enable typing */}
              <ContactDetailsPanel details={formData} onFormChange={handleInputChange} />

              {/* A new grid for the content below the cards */}
              <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 items-start">
                <div className="lg:col-span-3">
                  <FaqPanel 
                      faqs={formData.faqs} 
                      onFaqChange={handleFaqChange} 
                      onAddFaq={addFaq} 
                      onRemoveFaq={removeFaq} 
                      newFaqIndex={newFaqIndex} 
                      onConfirmNewFaq={handleConfirmNewFaq}
                  />
                </div>
                <div className="lg:col-span-2">
                  <FormLabelsPanel labels={formData.formLabels} onLabelChange={handleFormLabelChange} />
                </div>
              </div>
            </div>
          )}
          {activeTab === 'about' && (
            <AboutPanel
              aboutData={formData.aboutUs || {}}
              onAboutChange={handleInputChange}
            />
          )}
          
          {/* ... (Fixed Save Button footer is unchanged) ... */}
          <motion.div 
            className="fixed bottom-0 left-0 lg:left-60 right-0 p-4 z-50" 
            initial={{ y: 120 }} 
            animate={{ y: 0 }} 
            transition={{ type: 'spring', stiffness: 200, damping: 30 }}
          >
            <div className="max-w-7xl mx-auto flex justify-between items-center p-4 rounded-xl bg-white/80 backdrop-blur-xl shadow-2xl shadow-slate-900/10 border border-slate-200/80">
                <div>
                  {error && <div className="text-sm text-red-600 flex items-center"><AlertCircle className="w-4 h-4 mr-2 shrink-0"/>{error}</div>}
                  {successMessage && <div className="text-sm text-green-600 flex items-center"><Sparkles className="w-4 h-4 mr-2 text-green-500"/>{successMessage}</div>}
                </div>
                <button type="submit" disabled={isSaveDisabled} className="save-button">
                  {isSaving ? <Loader className="w-5 h-5 animate-spin"/> : <><Save className="w-5 h-5"/><span>Save Changes</span></>}
                </button>
            </div>
          </motion.div>
        </form>
      </div>
    </div>
  );
};

export default InfoControl;