/* AboutPanel.css */

.about-panel-container {
  @apply bg-white rounded-2xl shadow-lg shadow-slate-200/50 border border-slate-100 overflow-hidden;
}

.about-panel-nav {
  @apply border-b border-slate-200 p-6;
}

.about-panel-nav h2 {
  @apply text-2xl font-bold text-slate-800 mb-4;
}

.about-panel-nav-buttons {
  @apply flex flex-wrap gap-2;
}

.about-panel-nav-button {
  @apply flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors;
}

.about-panel-nav-button.active {
  @apply bg-blue-100 text-blue-700 border border-blue-200;
}

.about-panel-nav-button:not(.active) {
  @apply bg-slate-50 text-slate-600 hover:bg-slate-100 border border-slate-200;
}

.about-panel-content {
  @apply p-6;
}

.about-section {
  @apply space-y-6;
}

.about-section-title {
  @apply text-xl font-semibold text-slate-800 flex items-center gap-2;
}

.about-form-group {
  @apply space-y-4;
}

.about-form-label {
  @apply block text-sm font-medium text-slate-700 mb-2;
}

.about-form-input {
  @apply w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.about-form-textarea {
  @apply w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.about-array-item {
  @apply flex items-center gap-2;
}

.about-add-button {
  @apply flex items-center gap-2 px-4 py-2 text-blue-600 border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors;
}

.about-remove-button {
  @apply p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors;
}

/* Team member and story milestone cards */
.about-card {
  @apply bg-slate-50 border border-slate-200 rounded-lg p-4;
}

.about-card-header {
  @apply flex items-center justify-between mb-3;
}

.about-card-title {
  @apply font-medium text-slate-800;
}

.about-card-actions {
  @apply flex items-center gap-2;
}

.about-card-action-button {
  @apply p-1.5 rounded-md transition-colors;
}

.about-card-action-button.edit {
  @apply text-blue-600 hover:bg-blue-100;
}

.about-card-action-button.delete {
  @apply text-red-600 hover:bg-red-100;
}

/* Form grid layouts */
.about-grid-2 {
  @apply grid grid-cols-2 gap-4;
}

.about-grid-3 {
  @apply grid grid-cols-3 gap-4;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .about-grid-2,
  .about-grid-3 {
    @apply grid-cols-1;
  }

  .about-panel-nav-buttons {
    @apply flex-col;
  }

  .about-panel-nav-button {
    @apply justify-start;
  }
}