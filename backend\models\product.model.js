// backend/models/product.model.js

import mongoose from 'mongoose';

const productSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: [true, 'Product name is required.'],
            trim: true,
        },
        description: {
            type: String,
            required: [true, 'Product description is required.'],
        },
        price: {
            type: Number,
            required: [true, 'Product price is required.'],
        },
        originalPrice: {
            type: Number,
        },
        category: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Category',
            required: true,
        },
        // --- MODIFIED: Upgraded to support multiple variants ---
        colors: {
            type: [String],
            required: true,
            validate: [val => val.length > 0, 'At least one color is required.']
        },
        sizes: {
            type: [String],
            required: true,
            validate: [val => val.length > 0, 'At least one size is required.']
        },
        isNew: {
            type: Boolean,
            default: false,
        },
        // --- END MODIFICATION ---
        imageUrl: {
            type: String,
            required: true,
        },
        thumbnailUrl: {
            type: String,
            required: true,
        },
    },
    {
        timestamps: true, // Automatically adds createdAt and updatedAt fields
    }
);

const Product = mongoose.model('Product', productSchema);

export default Product;