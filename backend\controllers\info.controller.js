// backend/controllers/info.controller.js
import Info from '../models/Info.model.js';

// Get (or create if not exists) the single info document
export const getOrCreateInfo = async (req, res) => {
  try {
    let info = await Info.findOne();
    if (!info) {
      // If no info doc exists, create one with default values from the schema
      info = await Info.create({
        email: '<EMAIL>',
        phone: '(+1) ************',
        address: '123 Design St, Creative City',
        googleMapsUrl: 'https://www.google.com/maps/place/Times+Square',
        formLabels: { // Initialize the sub-document
          name: 'Full Name',
          email: 'Email Address',
          message: 'Your Message'
        },
        faqs: [],
        aboutUs: {
          // Hero Section defaults
          heroTitle: 'Know About Us',
          heroDescription: 'We are a team of passionate creators, designers, and innovators dedicated to crafting exceptional digital experiences that drive results and inspire our community.',
          heroButtonText: 'Contact Us',
          heroButtonLink: '/contact',

          // Company Information defaults
          companyName: 'Your Company',
          foundedYear: '2020',

          // Mission, Vision, Values defaults
          mission: 'To create exceptional digital experiences that drive results and inspire communities.',
          vision: 'To be the leading force in digital innovation, transforming ideas into impactful solutions.',
          values: [
            'Innovation and creativity in everything we do',
            'Commitment to excellence and quality',
            'Collaboration and teamwork',
            'Integrity and transparency',
            'Customer-centric approach'
          ],

          // Story Section defaults
          storyTitle: 'An Unbroken Chain of Trust',
          storyDescription: 'A chronological showcase of our evolution, from a foundational idea to a living, breathing platform. Each milestone represents a core chapter in our story of purpose-driven innovation.',
          storyMilestones: [
            {
              year: "EST. 2020",
              milestone: "Inception",
              description: "In a digital landscape saturated with fleeting trends, a core question emerged: How can we build platforms with lasting purpose? This question became our foundational spark.",
              image: "https://i.pinimg.com/1200x/fd/96/24/fd9624ae8ebf666a6661ff666fa06c45.jpg",
              highlights: [
                { icon: "FiGitBranch", text: "Core Philosophy Defined" },
                { icon: "FiCode", text: "Initial Architectural Schematics" },
                { icon: "FiLayers", text: "Assembled Founding Team" }
              ]
            },
            {
              year: "2021-2022",
              milestone: "Genesis",
              description: "The architectural blueprints were translated into tangible code. This was a period of intense creation, forging a robust, scalable, and elegant framework designed for the future.",
              image: "https://i.pinimg.com/1200x/15/72/ff/1572ff60f781820b9cdb5b333f003304.jpg",
              highlights: [
                { icon: "FiCode", text: "Proprietary Engine v1.0" },
                { icon: "FiZap", text: "First Successful Stress Tests" },
                { icon: "FiTrendingUp", text: "Secured Seed Investment" }
              ]
            }
          ],

          // Team Section defaults
          teamTitle: 'The Visionaries Behind the Craft',
          teamDescription: 'We are a symphony of strategists, designers, and engineers, harmonizing to create unforgettable digital orchestrations.',
          teamMembers: [
            {
              name: "Aria Montgomery",
              title: "Lead Experience Architect",
              image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=1961&auto=format&fit=crop",
              description: "Aria architects the user's journey, transforming complex needs into intuitive and memorable interactions.",
              socials: { twitter: "#", linkedin: "#", dribbble: "#" }
            },
            {
              name: "Jaxson Cole",
              title: "Principal Engineer",
              image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1974&auto=format&fit=crop",
              description: "Jaxson is the bedrock of our technology, building robust, scalable systems that perform under pressure.",
              socials: { linkedin: "#", github: "#" }
            }
          ]
        }
      });
    }
    res.status(200).json(info);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching or creating info document', error: error.message });
  }
};

// Update the single info document
export const updateInfo = async (req, res) => {
  try {
    const updatedInfo = await Info.findOneAndUpdate({}, req.body, { new: true, runValidators: true });
    if (!updatedInfo) {
      return res.status(404).json({ message: "Info document not found. Could not update." });
    }
    res.status(200).json(updatedInfo);
  } catch (error) {
    res.status(400).json({ message: 'Error updating info', error: error.message });
  }
};