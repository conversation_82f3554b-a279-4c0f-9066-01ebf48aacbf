// backend/controllers/info.controller.js
import Info from '../models/Info.model.js';

// Get (or create if not exists) the single info document
export const getOrCreateInfo = async (req, res) => {
  try {
    let info = await Info.findOne();
    if (!info) {
      // If no info doc exists, create one with default values from the schema
      info = await Info.create({
        email: '<EMAIL>',
        phone: '(+1) ************',
        address: '123 Design St, Creative City',
        googleMapsUrl: 'https://www.google.com/maps/place/Times+Square',
        formLabels: { // Initialize the sub-document
          name: 'Full Name',
          email: 'Email Address',
          message: 'Your Message'
        },
        faqs: []
      });
    }
    res.status(200).json(info);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching or creating info document', error: error.message });
  }
};

// Update the single info document
export const updateInfo = async (req, res) => {
  try {
    const updatedInfo = await Info.findOneAndUpdate({}, req.body, { new: true, runValidators: true });
    if (!updatedInfo) {
      return res.status(404).json({ message: "Info document not found. Could not update." });
    }
    res.status(200).json(updatedInfo);
  } catch (error) {
    res.status(400).json({ message: 'Error updating info', error: error.message });
  }
};