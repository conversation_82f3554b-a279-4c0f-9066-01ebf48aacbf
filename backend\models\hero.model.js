import mongoose from 'mongoose';

const HeroSchema = new mongoose.Schema({
    title: { type: String, required: true },
    category: { type: String, required: true },
    description: { type: String, required: true },
    imageUrl: { type: String, required: true },
    thumbnailUrl: { type: String, required: true }, // Ensure this field is here
}, { timestamps: true });

const Hero = mongoose.model('Hero', HeroSchema);
export default Hero;