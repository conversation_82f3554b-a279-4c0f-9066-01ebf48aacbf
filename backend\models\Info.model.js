// backend/models/Info.model.js
import mongoose from 'mongoose';

const faqSchema = new mongoose.Schema({
  question: { type: String, required: true, trim: true },
  answer: { type: String, required: true, trim: true },
});

const formLabelsSchema = new mongoose.Schema({
  name: { type: String, default: 'Full Name', trim: true },
  email: { type: String, default: 'Email Address', trim: true },
  message: { type: String, default: 'Your Message', trim: true },
});

const infoSchema = new mongoose.Schema({
  // Hero Section
  heroTitle: { type: String, default: 'Get in Touch', trim: true },
  heroSubtitle: { type: String, default: "We're here to help and answer any question you might have. We look forward to hearing from you.", trim: true },
  
  // Core Contact Details
  email: { type: String, required: true, trim: true, lowercase: true },
  phone: { type: String, required: true, trim: true },
  address: { type: String, required: true, trim: true },
  googleMapsUrl: { type: String, required: true, trim: true },
  
  // Form Labels
  formLabels: formLabelsSchema,

  // FAQs
  faqs: [faqSchema]
}, { timestamps: true });

// To ensure there's only one document
infoSchema.pre('save', async function (next) {
  const count = await mongoose.model('Info').countDocuments();
  if (count > 0 && this.isNew) {
    next(new Error('Only one info document can exist. Please update the existing one.'));
  } else {
    next();
  }
});

const Info = mongoose.model('Info', infoSchema);
export default Info;