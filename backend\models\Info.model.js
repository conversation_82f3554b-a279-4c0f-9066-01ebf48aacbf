// backend/models/Info.model.js
import mongoose from 'mongoose';

const faqSchema = new mongoose.Schema({
  question: { type: String, required: true, trim: true },
  answer: { type: String, required: true, trim: true },
});

const formLabelsSchema = new mongoose.Schema({
  name: { type: String, default: 'Full Name', trim: true },
  email: { type: String, default: 'Email Address', trim: true },
  message: { type: String, default: 'Your Message', trim: true },
});

// About Us Schemas
const teamMemberSchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true },
  title: { type: String, required: true, trim: true },
  image: { type: String, required: true, trim: true },
  description: { type: String, required: true, trim: true },
  socials: {
    twitter: { type: String, trim: true },
    linkedin: { type: String, trim: true },
    github: { type: String, trim: true },
    dribbble: { type: String, trim: true },
  }
});

const storyMilestoneSchema = new mongoose.Schema({
  year: { type: String, required: true, trim: true },
  milestone: { type: String, required: true, trim: true },
  description: { type: String, required: true, trim: true },
  image: { type: String, required: true, trim: true },
  highlights: [{
    icon: { type: String, required: true, trim: true }, // Icon name/identifier
    text: { type: String, required: true, trim: true }
  }]
});

const aboutUsSchema = new mongoose.Schema({
  // Hero Section
  heroTitle: { type: String, default: 'Know About Us', trim: true },
  heroDescription: { type: String, default: 'We are a team of passionate creators, designers, and innovators dedicated to crafting exceptional digital experiences that drive results and inspire our community.', trim: true },
  heroButtonText: { type: String, default: 'Contact Us', trim: true },
  heroButtonLink: { type: String, default: '/contact', trim: true },

  // Company Information
  companyName: { type: String, default: 'Your Company', trim: true },
  foundedYear: { type: String, default: '2020', trim: true },

  // Mission, Vision, Values
  mission: { type: String, default: 'To create exceptional digital experiences that drive results and inspire communities.', trim: true },
  vision: { type: String, default: 'To be the leading force in digital innovation, transforming ideas into impactful solutions.', trim: true },
  values: [{ type: String, trim: true }], // Array of value statements

  // Story Section
  storyTitle: { type: String, default: 'An Unbroken Chain of Trust', trim: true },
  storyDescription: { type: String, default: 'A chronological showcase of our evolution, from a foundational idea to a living, breathing platform. Each milestone represents a core chapter in our story of purpose-driven innovation.', trim: true },
  storyMilestones: [storyMilestoneSchema],

  // Team Section
  teamTitle: { type: String, default: 'The Visionaries Behind the Craft', trim: true },
  teamDescription: { type: String, default: 'We are a symphony of strategists, designers, and engineers, harmonizing to create unforgettable digital orchestrations.', trim: true },
  teamMembers: [teamMemberSchema]
});

const infoSchema = new mongoose.Schema({
  // Contact Page Data
  // Hero Section
  heroTitle: { type: String, default: 'Get in Touch', trim: true },
  heroSubtitle: { type: String, default: "We're here to help and answer any question you might have. We look forward to hearing from you.", trim: true },

  // Core Contact Details
  email: { type: String, required: true, trim: true, lowercase: true },
  phone: { type: String, required: true, trim: true },
  address: { type: String, required: true, trim: true },
  googleMapsUrl: { type: String, required: true, trim: true },

  // Form Labels
  formLabels: formLabelsSchema,

  // FAQs
  faqs: [faqSchema],

  // About Us Page Data
  aboutUs: aboutUsSchema
}, { timestamps: true });

// To ensure there's only one document
infoSchema.pre('save', async function (next) {
  const count = await mongoose.model('Info').countDocuments();
  if (count > 0 && this.isNew) {
    next(new Error('Only one info document can exist. Please update the existing one.'));
  } else {
    next();
  }
});

const Info = mongoose.model('Info', infoSchema);
export default Info;