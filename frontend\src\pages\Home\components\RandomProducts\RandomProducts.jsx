    // frontend/src/pages/Home/components/RandomProducts/RandomProducts.jsx

    import React, { useState, useEffect, useRef } from 'react';
    import axios from 'axios';
    import { motion, AnimatePresence } from 'framer-motion';
    import { Loader, <PERSON><PERSON>Triangle, ArrowRight, ChevronLeft, ChevronRight, Zap, ShoppingBag } from 'lucide-react';
    import './RandomProducts.css';

    const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
    const AUTO_ADVANCE_DELAY = 3500;

    const COLOR_MAP = {
        'Obsidian Black': '#0a0f19', 'Graphite': '#374151', 'Stone Gray': '#9ca3af',
        'Pure White': '#ffffff', 'Crimson Red': '#dc2626', 'Midnight Blue': '#1e3a8a',
        'Forest Green': '#15803d', 'Desert Sand': '#fde68a',
    };

    const ShowcaseArrow = ({ direction, onClick }) => (
        <button onClick={onClick} className="showcase-arrow" aria-label={direction === 'left' ? 'Previous Product' : 'Next Product'}>
            {direction === 'left' ? <ChevronLeft size={24} /> : <ChevronRight size={24} />}
        </button>
    );

    // --- NEW: The "Action Capsule" - A Superior Interaction Model ---
    const ActionCapsule = ({ variants }) => {
        return (
            <motion.div variants={variants} className="action-capsule-container">
                <div className="action-capsule">
                    {/* --- The secondary actions are now positioned first for layout purposes --- */}
                    <div className="capsule-secondary-group">
                        <button className="capsule-button capsule-secondary">
                            <Zap size={18} />
                            <span>Purchase</span>
                        </button>
                        <button className="capsule-button capsule-secondary">
                            <ShoppingBag size={18} />
                            <span>Add to Bag</span>
                        </button>
                    </div>
                    {/* --- The primary action that is always visible --- */}
                    <button className="capsule-button capsule-primary">
                        <span>Explore Product</span>
                        <ArrowRight size={20} />
                    </button>
                </div>
            </motion.div>
        );
    };

    // --- MODIFIED: MainDisplay now uses the new ActionCapsule ---
    const MainDisplay = ({ product }) => {
        if (!product) return null;

        const imageVariants = {
            enter: { opacity: 0, scale: 1.05 },
            center: { opacity: 1, scale: 1, transition: { duration: 0.6, ease: [0.22, 1, 0.36, 1] } },
            exit: { opacity: 0, scale: 0.95, transition: { duration: 0.5 } },
        };
        
        const detailsVariants = {
            enter: { opacity: 0, y: 20 },
            center: { opacity: 1, y: 0, transition: { staggerChildren: 0.07, delayChildren: 0.3, duration: 0.6 } },
            exit: { opacity: 0, y: -20, transition: { duration: 0.4 } },
        };

        return (
            <div className="main-display-container">
                <div className="image-viewport">
                    <AnimatePresence initial={false} mode="wait">
                        <motion.div key={product._id} variants={imageVariants} initial="enter" animate="center" exit="exit" className="image-wrapper">
                            <img className="lookbook-main-image" src={`${API_BASE_URL}${product.imageUrl}`} alt={product.name} />
                        </motion.div>
                    </AnimatePresence>
                </div>
                <div className="details-panel">
                    <AnimatePresence mode="wait">
                        <motion.div key={product._id} variants={detailsVariants} initial="enter" animate="center" exit="exit" className="details-content">
                            <motion.span variants={detailsVariants} className="category-label">{product.category.name}</motion.span>
                            <motion.h3 variants={detailsVariants} className="product-title-lookbook">{product.name}</motion.h3>
                            <motion.p variants={detailsVariants} className="product-description-lookbook">{product.description}</motion.p>
                            
                            <motion.div variants={detailsVariants} className="price-section-lookbook">
                                <span className="current-price">${product.price.toFixed(2)}</span>
                                {product.originalPrice && <span className="original-price">${product.originalPrice.toFixed(2)}</span>}
                            </motion.div>

                            <motion.div variants={detailsVariants} className="attribute-section">
                            <h4 className="attribute-label">Available Colors</h4>
                            <div className="color-swatch-container">
                                {product.colors.map(colorName => (
                                    <div key={colorName} className="color-swatch" style={{ backgroundColor: COLOR_MAP[colorName] || '#cccccc' }} title={colorName}></div>
                                ))}
                            </div>
                            </motion.div>
                            
                            <motion.div variants={detailsVariants} className="attribute-section">
                                <h4 className="attribute-label">Sizes</h4>
                                <div className="size-tag-container">
                                    {product.sizes.map(size => (
                                        <div key={size} className="size-tag">{size}</div>
                                    ))}
                                </div>
                            </motion.div>
                            
                            {/* --- Replaced with the superior ActionCapsule --- */}
                            <ActionCapsule variants={detailsVariants} />
                        </motion.div>
                    </AnimatePresence>
                </div>
            </div>
        );
    };

    // --- The main component logic remains the same (No Changes Below) ---
    const RandomProducts = () => {
        const [products, setProducts] = useState([]);
        const [activeIndex, setActiveIndex] = useState(0);
        const [loading, setLoading] = useState(true);
        const [error, setError] = useState(null);
        const [isPaused, setIsPaused] = useState(false);
        const timerRef = useRef(null);

        useEffect(() => {
            const fetchProducts = async () => {
                try {
                    const { data } = await axios.get(`${API_BASE_URL}/api/products?isNew=true&limit=5`);
                    if (data && data.length > 0) {
                        setProducts(data);
                    } else { setError("No new items to showcase at the moment."); }
                } catch (err) { setError("Our Lookbook is currently unavailable.");
                } finally { setLoading(false); }
            };
            fetchProducts();
        }, []);

        const resetTimer = () => {
            clearTimeout(timerRef.current);
            if (!isPaused && products.length > 1) {
                timerRef.current = setTimeout(() => { goToNext(); }, AUTO_ADVANCE_DELAY);
            }
        };

        const goToNext = () => setActiveIndex(prev => (prev + 1) % products.length);
        const goToPrev = () => setActiveIndex(prev => (prev - 1 + products.length) % products.length);

        useEffect(() => {
            resetTimer();
            return () => clearTimeout(timerRef.current);
        }, [activeIndex, isPaused, products]);

        const handleTrayClick = (index) => setActiveIndex(index);

        if (loading) return <div className="status-wrapper"><Loader className="animate-spin text-gray-500" size={40} /></div>;
        if (error || products.length === 0) return <div className="status-wrapper"><AlertTriangle className="text-orange-500" size={40} /><p>{error || "Showcase is empty."}</p></div>;

        return (
            <section
                className="living-lookbook-section"
                onMouseEnter={() => setIsPaused(true)}
                onMouseLeave={() => setIsPaused(false)}
            >
                <div className="lookbook-container">
                    <div className="lookbook-title-area">
                        <h2 className="lookbook-title">The Designer's Showcase</h2>
                        <p className="lookbook-subtitle">A curated view of our latest designs, presented with composure.</p>
                    </div>
                    <MainDisplay product={products[activeIndex]} />

                    <div className="showcase-controls">
                        <ShowcaseArrow direction="left" onClick={goToPrev} />
                        <div className="lookbook-tray">
                            {products.map((p, index) => (
                                <button key={p._id} className={`tray-item-lookbook ${index === activeIndex ? 'is-active' : ''}`} onClick={() => handleTrayClick(index)}>
                                    <img src={`${API_BASE_URL}${p.thumbnailUrl}`} alt={p.name} />
                                    <div className="active-indicator">
                                        {index === activeIndex && !isPaused && (
                                        <div key={activeIndex} className="progress-bar" style={{ animationDuration: `${AUTO_ADVANCE_DELAY}ms` }} />
                                        )}
                                    </div>
                                </button>
                            ))}
                        </div>
                        <ShowcaseArrow direction="right" onClick={goToNext} />
                    </div>
                </div>
            </section>
        );
    };

    export default RandomProducts;