// backend/controllers/product.controller.js

import Product from '../models/product.model.js';
import Category from '../models/category.model.js';
import sharp from 'sharp';
import path from 'path';
import fs from 'fs';
import { promises as fsPromises } from 'fs';
import { fileURLToPath } from 'url';

// --- SETUP AND UTILITIES (No changes here) ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const uploadsDir = path.join(__dirname, '..', 'uploads', 'products');

if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
}

const processProductImage = async (buffer) => {
    const filename = `product-${Date.now()}-${Math.round(Math.random() * 1E9)}.webp`;
    const filepath = path.join(uploadsDir, filename);

    const thumbFilename = `thumb-${filename}`;
    const thumbFilepath = path.join(uploadsDir, thumbFilename);

    await sharp(buffer)
        .resize(800, 800, { fit: 'inside', withoutEnlargement: true })
        .toFormat('webp', { quality: 80 })
        .toFile(filepath);

    await sharp(buffer)
        .resize(400, 400, { fit: 'cover' })
        .toFormat('webp', { quality: 75 })
        .toFile(thumbFilepath);
        
    return {
        imageUrl: `/uploads/products/${filename}`,
        thumbnailUrl: `/uploads/products/${thumbFilename}`,
    };
};

const deleteImageFiles = async (imageUrl, thumbnailUrl) => {
    try {
        if (imageUrl) {
            const imagePath = path.join(__dirname, '..', imageUrl);
            await fsPromises.unlink(imagePath);
        }
        if (thumbnailUrl) {
            const thumbPath = path.join(__dirname, '..', thumbnailUrl);
            await fsPromises.unlink(thumbPath);
        }
    } catch (error) {
        if (error.code !== 'ENOENT') {
            console.error("Error deleting image files:", error.message);
        }
    }
};

// --- Helper to parse array data from FormData ---
const parseArray = (value) => {
    if (!value) return [];
    if (Array.isArray(value)) return value;
    // Handles comma-separated strings from FormData
    return value.split(',').map(item => item.trim());
};

export const createProduct = async (req, res) => {
    // --- MODIFIED: Handle new fields ---
    const { name, description, price, originalPrice, categoryId, colors, sizes, isNew } = req.body;
    
    if (!req.file) {
        return res.status(400).json({ message: 'Product image is required.' });
    }

    const parsedColors = parseArray(colors);
    const parsedSizes = parseArray(sizes);

    if (!name || !description || !price || !categoryId || parsedColors.length === 0 || parsedSizes.length === 0) {
        return res.status(400).json({ message: 'Please fill all required fields, including at least one color and size.' });
    }

    try {
        const categoryExists = await Category.findById(categoryId);
        if (!categoryExists) {
            return res.status(404).json({ message: 'The selected category does not exist.' });
        }

        const { imageUrl, thumbnailUrl } = await processProductImage(req.file.buffer);

        const product = await Product.create({
            name,
            description,
            price: parseFloat(price),
            originalPrice: originalPrice ? parseFloat(originalPrice) : undefined,
            category: categoryId,
            colors: parsedColors,
            sizes: parsedSizes,
            isNew: isNew === 'true' || isNew === true,
            imageUrl,
            thumbnailUrl,
        });

        const newProduct = await Product.findById(product._id).populate('category', 'name');
        res.status(201).json(newProduct);

    } catch (error) {
        console.error('--- CREATE PRODUCT FAILED ---', error);
        res.status(500).json({
            message: 'Server Error: Could not create the product.',
            error: error.message
        });
    }
};

// @desc    Get all products, with optional filtering
// @route   GET /api/products
// @access  Public
export const getAllProducts = async (req, res) => {
    try {
        // --- MODIFICATION: Destructure new query parameters ---
        const { categoryId, isNew, limit } = req.query;
        const filter = {};

        if (categoryId) {
            filter.category = categoryId;
        }

        // --- NEW: Add filtering for 'isNew' products ---
        if (isNew === 'true') {
            filter.isNew = true;
        }

        // Build the base query
        let query = Product.find(filter)
            .populate('category', 'name')
            .sort({ createdAt: -1 });

        // --- NEW: Add a limit if provided ---
        if (limit) {
            const parsedLimit = parseInt(limit, 10);
            if (!isNaN(parsedLimit)) {
                query = query.limit(parsedLimit);
            }
        }
        
        const products = await query;
        res.status(200).json(products);

    } catch (error) {
        console.error('--- GET ALL PRODUCTS FAILED ---', error);
        res.status(500).json({ message: 'Server Error', error: error.message });
    }
};

export const updateProduct = async (req, res) => {
    const { id } = req.params;
    // --- MODIFIED: Handle new fields ---
    const { name, description, price, originalPrice, categoryId, colors, sizes, isNew } = req.body;

    try {
        const productToUpdate = await Product.findById(id);
        if (!productToUpdate) {
            return res.status(404).json({ message: 'Product not found.' });
        }

        const parsedColors = parseArray(colors);
        const parsedSizes = parseArray(sizes);

        const updateData = {
            name,
            description,
            price: parseFloat(price),
            originalPrice: (originalPrice && parseFloat(originalPrice)) ? parseFloat(originalPrice) : undefined,
            category: categoryId,
            colors: parsedColors,
            sizes: parsedSizes,
            isNew: isNew === 'true' || isNew === true,
        };
        
        if (req.file) {
            await deleteImageFiles(productToUpdate.imageUrl, productToUpdate.thumbnailUrl);
            const { imageUrl, thumbnailUrl } = await processProductImage(req.file.buffer);
            updateData.imageUrl = imageUrl;
            updateData.thumbnailUrl = thumbnailUrl;
        }

        const updatedProduct = await Product.findByIdAndUpdate(id, updateData, { new: true, runValidators: true })
            .populate('category', 'name');

        res.status(200).json(updatedProduct);

    } catch (error) {
        console.error('--- UPDATE PRODUCT FAILED ---', error);
        res.status(500).json({
            message: 'Server Error: Could not update the product.',
            error: error.message
        });
    }
};

export const deleteProduct = async (req, res) => {
    // ... (no changes in this function)
    const { id } = req.params;

    try {
        const product = await Product.findById(id);
        if (!product) {
            return res.status(404).json({ message: 'Product not found.' });
        }

        await deleteImageFiles(product.imageUrl, product.thumbnailUrl);
        await Product.findByIdAndDelete(id);

        res.status(200).json({ message: 'Product and associated images deleted successfully.', deletedProductId: id });

    } catch (error) {
        console.error('--- DELETE PRODUCT FAILED ---', error);
        res.status(500).json({
            message: 'Server Error: Could not delete the product.',
            error: error.message
        });
    }
};