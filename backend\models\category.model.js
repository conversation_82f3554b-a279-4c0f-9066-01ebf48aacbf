// backend/models/category.model.js

import mongoose from 'mongoose';

const categorySchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Category name is required.'],
        trim: true,
        unique: true,
    },
    // --- THE PROFESSIONAL METHOD ---
    // We store URL paths, not the entire image file. This keeps the document light and fast.
    imageUrl: {
        type: String,
        required: [true, 'Main image URL is required.'],
    },
    thumbnailUrl: {
        type: String,
        required: [true, 'Thumbnail image URL is required.'],
    }
}, {
    timestamps: true,
    // CRUCIAL: These options ensure virtual fields are included in API responses.
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});

// VIRTUAL for product count.
// This remains a brilliant and highly efficient feature. It dynamically counts
// related products without bloating the category document.
categorySchema.virtual('productCount', {
    ref: 'Product',           // The model to use for the count
    localField: '_id',        // Find where `_id` in this schema...
    foreignField: 'category', // ...matches the `category` field in the Product schema
    count: true               // And return the total count.
});

const Category = mongoose.model('Category', categorySchema);

export default Category;