// backend/controllers/category.controller.js

import Category from '../models/category.model.js';
import Product from '../models/product.model.js'; // Essential for 'populate' to work
import sharp from 'sharp';      // Import sharp for powerful image processing
import path from 'path';        // Import path for handling file paths
import fs from 'fs';            // Import fs (File System) to manage files/directories
import { fileURLToPath } from 'url';

// --- SETUP FOR FILE UPLOADS ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
// We create a dedicated 'categories' subfolder within 'uploads' for better organization.
const uploadsDir = path.join(__dirname, '..', 'uploads', 'categories');

// Ensure the target directory exists. If not, create it recursively.
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
}

// --- POWERFUL IMAGE PROCESSING UTILITY ---
// This reusable function handles image optimization and thumbnail generation.
const processImage = async (buffer) => {
    const filename = `category-${Date.now()}.webp`;
    const filepath = path.join(uploadsDir, filename);

    const thumbFilename = `thumb-${filename}`;
    const thumbFilepath = path.join(uploadsDir, thumbFilename);

    // Create a high-quality main image (e.g., for a category page banner)
    // Resized, converted to WebP format for superior compression.
    await sharp(buffer)
        .resize(1280, 720, { fit: 'inside', withoutEnlargement: true })
        .toFormat('webp', { quality: 85 })
        .toFile(filepath);

    // Create a small, fast-loading thumbnail for card views and lists.
    await sharp(buffer)
        .resize(400, 400, { fit: 'cover' })
        .toFormat('webp', { quality: 75 })
        .toFile(thumbFilepath);

    // Return the public-facing URL paths to be stored in the database.
    return {
        imageUrl: `/uploads/categories/${filename}`,
        thumbnailUrl: `/uploads/categories/${thumbFilename}`,
    };
};

// @desc    Create a new category
// @route   POST /api/categories
// @access  Private
export const createCategory = async (req, res) => {
    // We now expect multipart/form-data, so we get the name from req.body and file from req.file
    const { name } = req.body;
    if (!req.file) {
        return res.status(400).json({ message: 'Category image is required.' });
    }
    if (!name) {
        return res.status(400).json({ message: 'Category name is required.' });
    }

    try {
        const categoryExists = await Category.findOne({ name });
        if (categoryExists) {
            return res.status(400).json({ message: 'A category with this name already exists.' });
        }
        
        // Process the uploaded image file buffer with our powerful utility
        const { imageUrl, thumbnailUrl } = await processImage(req.file.buffer);

        // Create the new category with the file paths, not the file data
        const category = await Category.create({
            name,
            imageUrl,       // Save the URL path
            thumbnailUrl,   // Save the thumbnail URL path
        });
        
        await category.populate('productCount');
        res.status(201).json(category);

    } catch (error) {
        console.error('--- CREATE CATEGORY FAILED ---', error);
        res.status(500).json({ 
            message: 'Server Error: Could not create the category.',
            error: error.message 
        });
    }
};

// @desc    Get all categories
// @route   GET /api/categories
// @access  Public
export const getAllCategories = async (req, res) => {
    try {
        // This query is now extremely fast because it only fetches text data.
        const categories = await Category.find({}).populate('productCount').sort({ createdAt: -1 });
        res.status(200).json(categories);
    } catch (error) {
        console.error('--- GET ALL CATEGORIES FAILED ---', error);
        res.status(500).json({ message: 'Server Error', error: error.message });
    }
};

// --- NEW: THE DELETE CATEGORY FUNCTION ---
// @desc    Delete a category
// @route   DELETE /api/categories/:id
// @access  Private (for admins)
export const deleteCategory = async (req, res) => {
    try {
        const category = await Category.findById(req.params.id);

        if (!category) {
            return res.status(404).json({ message: 'Category not found.' });
        }

        // --- INTELLIGENT FILE CLEANUP ---
        // Construct the full server path for the main image and thumbnail.
        // We navigate from this controller's directory up to the 'backend' root,
        // then into the public-facing 'uploads' directory.
        const backendRoot = path.join(__dirname, '..');
        const imagePath = path.join(backendRoot, category.imageUrl);
        const thumbPath = path.join(backendRoot, category.thumbnailUrl);

        // Asynchronously check if the files exist and delete them to prevent orphaned data.
        // This is safer than sync versions in a high-concurrency environment.
        try {
            if (fs.existsSync(imagePath)) {
                await fs.promises.unlink(imagePath);
            }
            if (fs.existsSync(thumbPath)) {
                await fs.promises.unlink(thumbPath);
            }
        } catch (fileError) {
             // Log the error but don't stop the process. The main goal is to remove the DB entry.
            console.error('--- FILE CLEANUP WARNING ---', fileError);
        }

        // After cleaning up files, delete the category from the database.
        await Category.findByIdAndDelete(req.params.id);

        res.status(200).json({ message: 'Category and associated images deleted successfully.' });

    } catch (error) {
        console.error('--- DELETE CATEGORY FAILED ---', error);
        res.status(500).json({
            message: 'Server Error: Could not delete the category.',
            error: error.message,
        });
    }
};
// --- NEW: GET PRODUCTS FOR A SPECIFIC CATEGORY (FOR MEGAMENU) ---
// @desc    Get top 2 products for a specific category
// @route   GET /api/categories/:id/products
// @access  Public
export const getProductsForCategory = async (req, res) => {
    try {
        // Find products where the 'category' field matches the provided category ID.
        // We limit the result to 2 for a clean mega menu preview.
        // We sort by creation date to show the newest items.
        const products = await Product.find({ category: req.params.id })
            .sort({ createdAt: -1 })
            .limit(2);

        res.status(200).json(products);
    } catch (error) {
        console.error('--- GET PRODUCTS FOR CATEGORY FAILED ---', error);
        res.status(500).json({ 
            message: 'Server Error: Could not fetch products for this category.',
            error: error.message 
        });
    }
};