import React, { useState, useEffect } from 'react';
import { FiTwi<PERSON>, FiLinkedin, FiGithub, FiDribbble } from 'react-icons/fi';
import axios from 'axios';
import './TeamSection.css'; // The source of our "Kinetic Reveal"

const defaultTeamData = [
    {
        name: "<PERSON>",
        title: "Lead Experience Architect",
        image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=1961&auto=format&fit=crop",
        description: "Aria architects the user's journey, transforming complex needs into intuitive and memorable interactions.",
        socials: { twitter: "#", linkedin: "#", dribbble: "#" }
    },
    {
        name: "<PERSON><PERSON>",
        title: "Principal Engineer",
        image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1974&auto=format&fit=crop",
        description: "<PERSON><PERSON> is the bedrock of our technology, building robust, scalable systems that perform under pressure.",
        socials: { linkedin: "#", github: "#" }
    },
    {
        name: "<PERSON>",
        title: "Head of Product Design",
        image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=2070&auto=format&fit=crop",
        description: "<PERSON>'s vision shapes the product, ensuring every pixel serves a purpose and every feature delights the user.",
        socials: { twitter: "#", linkedin: "#" }
    },
    {
        name: "Kenji <PERSON>",
        title: "Creative Technologist",
        image: "https://images.unsplash.com/photo-1568602471122-7832951cc4c5?q=80&w=2070&auto=format&fit=crop",
        description: "Bridging the gap between design and code, Kenji brings static concepts to life with fluid animation.",
        socials: { twitter: "#", github: "#", dribbble: "#" }
    },
];

const TeamSection = () => {
  const [teamData, setTeamData] = useState(defaultTeamData);
  const [teamTitle, setTeamTitle] = useState('The Visionaries Behind the Craft');
  const [teamDescription, setTeamDescription] = useState('We are a symphony of strategists, designers, and engineers, harmonizing to create unforgettable digital orchestrations.');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAboutData = async () => {
      try {
        const response = await axios.get('http://localhost:5000/api/info');
        if (response.data.aboutUs) {
          const aboutData = response.data.aboutUs;

          // Update team title and description
          if (aboutData.teamTitle) setTeamTitle(aboutData.teamTitle);
          if (aboutData.teamDescription) setTeamDescription(aboutData.teamDescription);

          // Update team members if available
          if (aboutData.teamMembers && aboutData.teamMembers.length > 0) {
            setTeamData(aboutData.teamMembers);
          }
        }
      } catch (error) {
        console.error("Failed to fetch about data:", error);
        // Keep default values if API fails
      } finally {
        setLoading(false);
      }
    };
    fetchAboutData();
  }, []);

  if (loading) {
    return (
      <section className="bg-slate-100 font-sans py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </section>
    );
  }
  return (
    <section className="bg-slate-100 font-sans py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-slate-900 sm:text-4xl">
            {teamTitle}
          </h2>
          <p className="mt-6 text-lg leading-8 text-slate-600">
            {teamDescription}
          </p>
        </div>

        <div className="mx-auto mt-20 grid max-w-2xl grid-cols-1 gap-8 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:grid-cols-4 lg:gap-8">
          {teamData.map((member) => (
            <div key={member.name} className="team-card">
              <img src={member.image} alt={member.name} className="profile-image" />
              <div className="dark-overlay"></div>
              
              <div className="content-container">
                  <h3 className="member-name">{member.name}</h3>
                  <p className="member-title">{member.title}</p>
                  
                  <div className="revealed-content">
                    <p className="member-description">{member.description}</p>
                    <div className="social-links">
                      {member.socials.twitter && <a href={member.socials.twitter} className="social-link"><FiTwitter /></a>}
                      {member.socials.linkedin && <a href={member.socials.linkedin} className="social-link"><FiLinkedin /></a>}
                      {member.socials.github && <a href={member.socials.github} className="social-link"><FiGithub /></a>}
                      {member.socials.dribbble && <a href={member.socials.dribbble} className="social-link"><FiDribbble /></a>}
                    </div>
                  </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TeamSection;