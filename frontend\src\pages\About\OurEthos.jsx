// src/pages/About/OurEthos.jsx

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { FiArrowLeft, FiArrowRight, FiZap, FiLayers, FiCode, FiTrendingUp, FiGitBranch } from 'react-icons/fi';
import axios from 'axios';
import './OurEthos.css';

// --- Configuration ---
const AUTOPLAY_INTERVAL = 10000; // 10 seconds
const TRANSITION_DURATION = 1200;

// Icon mapping for dynamic data
const iconMap = {
  'FiGitBranch': <FiGitBranch />,
  'FiCode': <FiCode />,
  'FiLayers': <FiLayers />,
  'FiZap': <FiZap />,
  'FiTrendingUp': <FiTrendingUp />
};

// Default story data as fallback
const defaultStoryData = [
    { year: "EST. 2020", milestone: "Inception", description: "In a digital landscape saturated with fleeting trends, a core question emerged: How can we build platforms with lasting purpose? This question became our foundational spark.", image: "https://i.pinimg.com/1200x/fd/96/24/fd9624ae8ebf666a6661ff666fa06c45.jpg", highlights: [{ icon: "FiGitBranch", text: "Core Philosophy Defined" }, { icon: "FiCode", text: "Initial Architectural Schematics" }, { icon: "FiLayers", text: "Assembled Founding Team" }] },
    { year: "2021-2022", milestone: "Genesis", description: "The architectural blueprints were translated into tangible code. This was a period of intense creation, forging a robust, scalable, and elegant framework designed for the future.", image: "https://i.pinimg.com/1200x/15/72/ff/1572ff60f781820b9cdb5b333f003304.jpg", highlights: [{ icon: "FiCode", text: "Proprietary Engine v1.0" }, { icon: "FiZap", text: "First Successful Stress Tests" }, { icon: "FiTrendingUp", text: "Secured Seed Investment" }] },
    { year: "2023-2024", milestone: "Quantum Leap", description: "Our first platforms went live, transitioning from theoretical models to real-world impact. This wasn't just a launch; it was a validation of our entire ethos.", image: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=2000", highlights: [{ icon: "FiZap", text: "Inaugural Client Platform Deployed" }, { icon: "FiLayers", text: "Scaled Infrastructure by 300%" }, { icon: "FiTrendingUp", text: "Achieved Operational Profitability" }] },
    { year: "THE HORIZON", milestone: "Symbiosis", description: "Today, we operate in a state of perpetual evolution. Our growth is intertwined with our partners' ambitions, creating a symbiotic relationship that pushes the boundaries of what's possible.", image: "https://i.pinimg.com/1200x/4e/74/e8/4e74e8d3ba932b00a97997e60f7bba6.jpg", highlights: [{ icon: "FiGitBranch", text: "Expanding into New Verticals" }, { icon: "FiLayers", text: "AI-Driven Analytics Integration" }, { icon: "FiCode", text: "Commitment to Open Source" }] },
];


const OurEthos = () => {
  const [activeSlide, setActiveSlide] = useState(0);
  const [exitingSlide, setExitingSlide] = useState(-1);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [storyData, setStoryData] = useState(defaultStoryData);
  const [storyTitle, setStoryTitle] = useState('An Unbroken Chain of Trust');
  const [storyDescription, setStoryDescription] = useState('A chronological showcase of our evolution, from a foundational idea to a living, breathing platform. Each milestone represents a core chapter in our story of purpose-driven innovation.');
  const [loading, setLoading] = useState(true);
  const autoplayTimer = useRef(null);

  useEffect(() => {
    const fetchAboutData = async () => {
      try {
        const response = await axios.get('http://localhost:5000/api/info');
        if (response.data.aboutUs) {
          const aboutData = response.data.aboutUs;

          // Update story title and description
          if (aboutData.storyTitle) setStoryTitle(aboutData.storyTitle);
          if (aboutData.storyDescription) setStoryDescription(aboutData.storyDescription);

          // Update story milestones if available
          if (aboutData.storyMilestones && aboutData.storyMilestones.length > 0) {
            setStoryData(aboutData.storyMilestones);
          }
        }
      } catch (error) {
        console.error("Failed to fetch about data:", error);
        // Keep default values if API fails
      } finally {
        setLoading(false);
      }
    };
    fetchAboutData();
  }, []);

  const navigate = useCallback((direction) => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setExitingSlide(activeSlide);

    const nextSlideIndex = direction === 'next'
      ? (activeSlide + 1) % storyData.length
      : (activeSlide - 1 + storyData.length) % storyData.length;
    
    setActiveSlide(nextSlideIndex);

    setTimeout(() => {
      setExitingSlide(-1);
      setIsTransitioning(false);
    }, TRANSITION_DURATION);
  }, [activeSlide, isTransitioning]);

  const runAutoplay = useCallback(() => {
    clearTimeout(autoplayTimer.current);
    autoplayTimer.current = setTimeout(() => navigate('next'), AUTOPLAY_INTERVAL);
  }, [navigate]);

  useEffect(() => {
    runAutoplay();
    return () => clearTimeout(autoplayTimer.current);
  }, [activeSlide, runAutoplay]);

  const handleNavigationClick = (direction) => {
    clearTimeout(autoplayTimer.current);
    navigate(direction);
  };

  return (
    <div className="our-ethos-wrapper">
      {/* --- Section Header --- */}
      <div className="ethos-header">
        <h2 className="ethos-title">{storyTitle}</h2>
        <p className="ethos-description">
          {storyDescription}
        </p>
      </div>

      {/* --- Main Component --- */}
      <section className="aperture-section  ">
        {/* Column 1: Image Viewfinder */}
        <div className="image-viewfinder">
          {storyData.map((slide, index) => {
            let stateClass = '';
            if (index === activeSlide) stateClass = 'slide-active';
            if (index === exitingSlide) stateClass = 'slide-exiting';

            return (
              <div key={index} className={`slide-background ${stateClass}`}>
                <div
                  className="slide-background-image"
                  style={{ backgroundImage: `url(${slide.image})` }}
                />
              </div>
            );
          })}
          <div className={`viewfinder-reticle ${activeSlide !== -1 ? 'slide-active' : ''}`} />
        </div>

        {/* Column 2: Text Console */}
        <div className="text-console ">
          <div className={`console-connector ${activeSlide !== -1 ? 'slide-active' : ''}`} />
          <div className="console-content">
            {storyData.map((slide, index) => index === activeSlide && (
              <React.Fragment key={slide.year}>
                <div className="slide-milestone-mask">
                  <h2 className="slide-milestone">
                    {slide.milestone.split('').map((char, i) => (
                      <span key={i} style={{ animationDelay: `${200 + i * 40}ms` }}>
                        {char === ' ' ? '\u00A0' : char}
                      </span>
                    ))}
                  </h2>
                </div>
                <p className="slide-year">{slide.year}</p>
                
                <div className="description-mask">
                  <p className="slide-description">{slide.description}</p>
                </div>

                <div className="description-mask">
                   <h3 className="highlights-title">Key Developments</h3>
                </div>
                
                <ul className="highlights-list">
                  {slide.highlights.map((item, i) => (
                    <li key={i} className="highlight-item" style={{ animationDelay: `${1000 + i * 150}ms` }}>
                      {iconMap[item.icon] || <FiCode />}
                      <span>{item.text}</span>
                    </li>
                  ))}
                </ul>
              </React.Fragment>
            ))}
          </div>

          <div className="console-footer">
            <div className="aperture-navigation">
              <button onClick={() => handleNavigationClick('prev')} className="nav-arrow" aria-label="Previous Moment"><FiArrowLeft /></button>
              <button onClick={() => handleNavigationClick('next')} className="nav-arrow" aria-label="Next Moment"><FiArrowRight /></button>
            </div>
            <div className="progress-bar-container">
              <div key={activeSlide} className="progress-bar" />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default OurEthos;