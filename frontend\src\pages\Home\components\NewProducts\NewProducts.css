/* src/pages/Home/components/NewProducts/FeaturedProducts.css */

.featured-products-section {
    position: relative;
    padding: 6rem 0;
    overflow: hidden;
}

/* --- Section Title --- */
.section-title {
    text-align: center;
    font-size: 3rem; /* 48px */
    font-weight: 800;
    letter-spacing: -0.025em;
    color: #F9FAFB; /* gray-50 */
    margin-bottom: 0.5rem;
    background: linear-gradient(to right, #FFFFFF, #9CA3AF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.section-subtitle {
    text-align: center;
    font-size: 1.125rem; /* 18px */
    color: #9CA3AF; /* gray-400 */
    max-width: 600px;
    margin: 0 auto 4rem auto;
}

/* --- Swiper Container & Slide Styling --- */
.swiper-container-featured {
    position: relative;
    padding: 0 4rem; /* Make space for navigation buttons */
}

/* We target the swiper-slide-visible to apply transitions smoothly */
.swiper-slide {
    width: 300px; /* Define a fixed width for consistency */
    height: 420px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1), filter 0.6s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.6s ease;
    filter: blur(4px) saturate(0.5);
    opacity: 0.6;
    transform: scale(0.85);
}

@media (min-width: 1024px) {
  .swiper-slide {
    width: 350px;
    height: 490px;
  }
}

/* --- THE MAGIC: Active slide is sharp and prominent --- */
.swiper-slide-active {
    filter: blur(0px) saturate(1);
    opacity: 1;
    transform: scale(1);
}

/* We need to apply the product card container's style on the slide itself */
.swiper-slide .product-card-container {
    width: 100%;
    height: 100%;
}


/* --- Custom Glassy Navigation Buttons --- */
.swiper-nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 52px;
    height: 52px;
    border-radius: 50%;
    cursor: pointer;
    
    display: flex;
    align-items: center;
    justify-content: center;
    
    color: #E5E7EB; /* gray-200 */
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);

    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.swiper-nav-button:hover {
    background-color: rgba(59, 130, 246, 0.2); /* blue-500 with opacity */
    border-color: rgba(59, 130, 246, 0.4);
    color: #FFFFFF;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.3);
}

.swiper-button-prev-custom {
    left: 10px;
}

.swiper-button-next-custom {
    right: 10px;
}

/* Swiper can add a 'disabled' class even with loop, good to handle */
.swiper-button-disabled {
    opacity: 0.3;
    cursor: not-allowed;
    pointer-events: none;
}